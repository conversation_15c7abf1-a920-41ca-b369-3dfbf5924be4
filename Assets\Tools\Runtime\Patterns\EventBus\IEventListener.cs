namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Interface for objects that listen to events from the EventBus.
    /// Implementing this interface ensures proper subscription and unsubscription.
    /// </summary>
    /// <typeparam name="T">The type of event to listen for.</typeparam>
    public interface IEventListener<T>
    {
        /// <summary>
        /// Subscribe to events of type T.
        /// This should typically be called in OnEnable for MonoBehaviour components.
        /// </summary>
        void Subscribe();

        /// <summary>
        /// Unsubscribe from events of type T.
        /// This should typically be called in OnDisable for MonoBehaviour components.
        /// </summary>
        void Unsubscribe();
    }
}
