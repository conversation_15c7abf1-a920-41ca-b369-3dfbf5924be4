using System;
using System.Collections.Generic;
using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// A type-safe static event bus implementation for Unity projects.
    /// Allows publishing and subscribing to events across different parts of the application.
    /// </summary>
    public static class EventBus
    {
        private static readonly Dictionary<Type, List<Delegate>> eventSubscriptions = new();
        private static readonly object subscriptionLock = new();

        /// <summary>
        /// Subscribe to events of type T.
        /// </summary>
        /// <typeparam name="T">The type of event to subscribe to.</typeparam>
        /// <param name="handler">The callback to invoke when the event is published.</param>
        public static void Subscribe<T>(Action<T> handler)
        {
            if (handler == null)
            {
                Debug.LogError("EventBus: Cannot subscribe with a null handler");
                return;
            }

            lock (subscriptionLock)
            {
                Type eventType = typeof(T);

                if (!eventSubscriptions.TryGetValue(eventType, out List<Delegate> handlers))
                {
                    handlers = new();
                    eventSubscriptions[eventType] = handlers;
                }

                if (!handlers.Contains(handler))
                {
                    handlers.Add(handler);
                }
                else
                {
                    Debug.LogWarning($"EventBus: Handler already subscribed for event type {eventType.Name}");
                }
            }
        }

        /// <summary>
        /// Unsubscribe from events of type T.
        /// </summary>
        /// <typeparam name="T">The type of event to unsubscribe from.</typeparam>
        /// <param name="handler">The callback to remove from the subscribers list.</param>
        public static void Unsubscribe<T>(Action<T> handler)
        {
            if (handler == null)
            {
                Debug.LogError("EventBus: Cannot unsubscribe with a null handler");
                return;
            }

            lock (subscriptionLock)
            {
                Type eventType = typeof(T);

                if (eventSubscriptions.TryGetValue(eventType, out List<Delegate> handlers))
                {
                    handlers.Remove(handler);

                    // Clean up empty lists
                    if (handlers.Count == 0)
                    {
                        eventSubscriptions.Remove(eventType);
                    }
                }
            }
        }

        /// <summary>
        /// Publish an event to all subscribers.
        /// </summary>
        /// <typeparam name="T">The type of event to publish.</typeparam>
        /// <param name="eventData">The event data to send to subscribers.</param>
        public static void Publish<T>(T eventData)
        {
            if (eventData == null)
            {
                Debug.LogError("EventBus: Cannot publish a null event");
                return;
            }

            List<Action<T>> handlersToInvoke = new();

            lock (subscriptionLock)
            {
                Type eventType = typeof(T);

                if (eventSubscriptions.TryGetValue(eventType, out List<Delegate> handlers))
                {
                    foreach (Delegate handler in handlers)
                    {
                        if (handler is Action<T> typedHandler)
                        {
                            handlersToInvoke.Add(typedHandler);
                        }
                    }
                }
            }

            // Invoke handlers outside the lock to prevent deadlocks
            foreach (Action<T> handler in handlersToInvoke)
            {
                try
                {
                    handler(eventData);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"EventBus: Error invoking handler for {typeof(T).Name}: {ex.Message}\n{ex.StackTrace}");
                }
            }
        }

        /// <summary>
        /// Clear all subscriptions for a specific event type.
        /// </summary>
        /// <typeparam name="T">The type of event to clear subscriptions for.</typeparam>
        public static void ClearSubscriptions<T>()
        {
            lock (subscriptionLock)
            {
                Type eventType = typeof(T);
                eventSubscriptions.Remove(eventType);
            }
        }

        /// <summary>
        /// Clear all event subscriptions.
        /// </summary>
        public static void ClearAllSubscriptions()
        {
            lock (subscriptionLock)
            {
                eventSubscriptions.Clear();
            }
        }
    }
}