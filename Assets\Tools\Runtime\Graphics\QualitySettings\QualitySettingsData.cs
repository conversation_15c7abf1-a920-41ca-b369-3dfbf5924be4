using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// ScriptableObject for storing quality settings thresholds.
    /// </summary>
    [CreateAssetMenu(fileName = "QualitySettingsData", menuName = "Scriptable Objects/Quality/Quality Settings Data")]
    public class QualitySettingsData : ScriptableObject
    {
        public QualityIndex qualityIndex = QualityIndex.Low;
        public int processorCount = 4;
        [Tooltip("Graphics memory in MB")]
        public int graphicsMemory = 2048;
        [Tooltip("System memory in MB")]
        public int systemMemory = 8192;
        [Tooltip("Processor frequency in MHz")]
        public int processorFrequency = 2400;
    }

    /// <summary>
    /// Enum for quality levels.
    /// </summary>
    public enum QualityIndex
    {
        VeryLow = 0,
        Low = 1,
        Medium = 2,
        High = 3,
        Ultra = 4
    }
}