using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// A ScriptableObject variable that holds a string value.
    /// </summary>
    [CreateAssetMenu(fileName = "StringVariable", menuName = "Scriptable Objects/Variables/String Variable")]
    public sealed class StringVariable : BaseVariable<string>
    {
        /// <summary>
        /// Concatenate two StringVariables
        /// </summary>
        public static string operator +(StringVariable a, StringVariable b) => a.value + b.value;

        /// <summary>
        /// Concatenate a StringVariable and a string
        /// </summary>
        public static string operator +(StringVariable a, string b) => a.value + b;

        /// <summary>
        /// Concatenate a string and a StringVariable
        /// </summary>
        public static string operator +(string a, StringVariable b) => a + b.value;

        /// <summary>
        /// Implicitly convert a StringVariable to a string
        /// </summary>
        public static implicit operator string(StringVariable variable) => variable.value;

        /// <summary>
        /// Check if the string is null or empty
        /// </summary>
        public bool IsNullOrEmpty() => string.IsNullOrEmpty(value);

        /// <summary>
        /// Check if the string is null or whitespace
        /// </summary>
        public bool IsNullOrWhiteSpace() => string.IsNullOrWhiteSpace(value);
    }
}