using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Detects trigger events and exposes them via UnityEvents.
    /// </summary>
    public class TriggerDetection : MonoBehaviour
    {
        [SerializeField]
        private UnityEvent<Collider> onTriggerEnterEvent = new UnityEvent<Collider>();
        [SerializeField]
        private UnityEvent<Collider> onTriggerExitEvent = new UnityEvent<Collider>();
        [SerializeField]
        private UnityEvent<Collider> onTriggerStayEvent = new UnityEvent<Collider>();

        /// <summary>
        /// Invoked when a trigger is entered.
        /// </summary>
        public UnityEvent<Collider> OnTriggerEnterEvent => onTriggerEnterEvent;
        /// <summary>
        /// Invoked when a trigger is exited.
        /// </summary>
        public UnityEvent<Collider> OnTriggerExitEvent => onTriggerExitEvent;
        /// <summary>
        /// Invoked while a trigger is being stayed in.
        /// </summary>
        public UnityEvent<Collider> OnTriggerStayEvent => onTriggerStayEvent;

        private void OnTriggerEnter(Collider other)
        {
            onTriggerEnterEvent.Invoke(other);
        }

        private void OnTriggerExit(Collider other)
        {
            onTriggerExitEvent.Invoke(other);
        }

        private void OnTriggerStay(Collider other)
        {
            onTriggerStayEvent.Invoke(other);
        }
    }
}