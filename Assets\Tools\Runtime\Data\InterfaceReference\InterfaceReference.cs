using System;
using UnityEngine;
using Object = UnityEngine.Object;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Provides serializable interface references for Unity Inspector.
    /// </summary>
    /// <typeparam name="TInterface">The interface type to reference</typeparam>
    /// <typeparam name="TObject">The Unity Object type implementing the interface</typeparam>
    [Serializable]
    public abstract class InterfaceReference<TInterface, TObject>
        where TObject : Object
        where TInterface : class
    {
        [SerializeField]
        private TObject underlyingValue;

        /// <summary>
        /// Gets or sets the interface implementation.
        /// </summary>
        public TInterface Value
        {
            get
            {
                if (underlyingValue == null) return null;

                if (underlyingValue is TInterface implementation)
                    return implementation;

                Debug.LogError($"Object {underlyingValue.name} of type {underlyingValue.GetType()} " +
                               $"does not implement interface {typeof(TInterface)}");
                return null;
            }
            set
            {
                if (value == null)
                {
                    underlyingValue = null;
                    return;
                }

                if (value is TObject obj)
                    underlyingValue = obj;
                else
                    Debug.LogError($"Value of type {value.GetType()} cannot be assigned to {typeof(TObject)}");
            }
        }

        /// <summary>
        /// Gets or sets the underlying Unity Object directly.
        /// </summary>
        public TObject UnderlyingValue
        {
            get => underlyingValue;
            set => underlyingValue = value;
        }

        /// <summary>
        /// Checks if the reference is valid and implements the interface.
        /// </summary>
        public bool IsValid => underlyingValue != null && underlyingValue is TInterface;

        /// <summary>
        /// Creates an empty interface reference.
        /// </summary>
        public InterfaceReference() { }

        /// <summary>
        /// Creates an interface reference from a Unity Object.
        /// </summary>
        /// <param name="target">The Unity Object to reference</param>
        public InterfaceReference(TObject target)
        {
            if (target != null && target is not TInterface)
            {
                Debug.LogError($"Object {target.name} does not implement {typeof(TInterface)}");
                return;
            }
            underlyingValue = target;
        }

        /// <summary>
        /// Creates an interface reference from an interface implementation.
        /// </summary>
        /// <param name="interface">The interface implementation to reference</param>
        public InterfaceReference(TInterface @interface)
        {
            if (@interface == null) return;

            if (@interface is TObject obj)
                underlyingValue = obj;
            else
                Debug.LogError($"Interface implementation of type {@interface.GetType()} " +
                               $"is not a {typeof(TObject)}");
        }

        /// <summary>
        /// Implicitly converts an InterfaceReference to its interface type.
        /// </summary>
        /// <param name="reference">The reference to convert</param>
        public static implicit operator TInterface(InterfaceReference<TInterface, TObject> reference)
            => reference?.Value;

        /// <summary>
        /// Implicitly converts an InterfaceReference to a boolean indicating if it's valid.
        /// </summary>
        /// <param name="reference">The reference to convert</param>
        public static implicit operator bool(InterfaceReference<TInterface, TObject> reference)
            => reference != null && reference.IsValid;

        /// <summary>
        /// Returns a string representation of this reference.
        /// </summary>
        /// <returns>A string describing the reference</returns>
        public override string ToString() =>
            IsValid ? $"[{typeof(TInterface).Name}] {underlyingValue.name}" : "None";
    }

    /// <summary>
    /// Simplified version that uses UnityEngine.Object as the base type.
    /// </summary>
    /// <typeparam name="TInterface">The interface type to reference</typeparam>
    [Serializable]
    public sealed class InterfaceReference<TInterface> : InterfaceReference<TInterface, Object>
        where TInterface : class
    {
        /// <summary>
        /// Creates an empty interface reference.
        /// </summary>
        public InterfaceReference() : base() { }

        /// <summary>
        /// Creates an interface reference from a Unity Object.
        /// </summary>
        /// <param name="target">The Unity Object to reference</param>
        public InterfaceReference(Object target) : base(target) { }

        /// <summary>
        /// Creates an interface reference from an interface implementation.
        /// </summary>
        /// <param name="interface">The interface implementation to reference</param>
        public InterfaceReference(TInterface @interface) : base(@interface) { }
    }
}