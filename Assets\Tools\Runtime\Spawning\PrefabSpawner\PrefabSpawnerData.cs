using System.Collections.Generic;
using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// ScriptableObject holding configuration data for the PrefabSpawner.
    /// </summary>
    [CreateAssetMenu(fileName = "PrefabSpawnerData", menuName = "Scriptable Objects/Prefab Spawner/Prefab Spawner Data")]
    public class PrefabSpawnerData : ScriptableObject
    {
        #region Spawn Settings

        [Header("Spawn Settings")]

        /// <summary>
        /// List of prefabs that can be spawned.
        /// </summary>
        public List<SpawnablePrefab> prefabs = new List<SpawnablePrefab>();

        /// <summary>
        /// Maximum distance from the target at which objects can spawn.
        /// </summary>
        public float spawnRange = 30f;

        /// <summary>
        /// Distance at which objects will be despawned.
        /// </summary>
        public float despawnRange = 50f;

        /// <summary>
        /// Reference angle for the view cone.
        /// </summary>
        public float referenceAngle = 110f;

        /// <summary>
        /// Y-axis range for spawning objects.
        /// </summary>
        public Vector2 ySpawnRange = new Vector2(0, 10);

        /// <summary>
        /// Maximum number of attempts to find a valid spawn position.
        /// </summary>
        public int maxSpawnAttempts = 10;

        /// <summary>
        /// Maximum number of objects that can be spawned concurrently.
        /// </summary>
        public int maxConcurrentObjects = 20;

        /// <summary>
        /// Layers considered as obstructions for spawning.
        /// </summary>
        public LayerMask obstructionLayers;

        /// <summary>
        /// Time interval between spawn attempts.
        /// </summary>
        public float spawnInterval = 1f;

        /// <summary>
        /// Radius used for collision checks when spawning.
        /// </summary>
        public float collisionCheckRadius = 1f;

        /// <summary>
        /// Length used for collision checks when spawning.
        /// </summary>
        public float collisionCheckLength = 1f;

        #endregion

        #region Pool Settings

        [Header("Pool Settings")]

        /// <summary>
        /// Default size of the object pool for each prefab.
        /// </summary>
        public int defaultPoolSize = 10;

        /// <summary>
        /// Maximum size of the object pool for each prefab.
        /// </summary>
        public int maxPoolSize = 50;

        #endregion

        #region Other Settings

        [Header("Other Settings")]

        /// <summary>
        /// Enables debug mode for additional logging and gizmos.
        /// </summary>
        public bool debugMode = false;

        #endregion
    }
}