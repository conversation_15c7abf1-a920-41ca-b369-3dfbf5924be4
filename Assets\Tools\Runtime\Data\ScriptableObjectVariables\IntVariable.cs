using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// A ScriptableObject variable that holds an integer value.
    /// </summary>
    [CreateAssetMenu(fileName = "IntVariable", menuName = "Scriptable Objects/Variables/Int Variable")]
    public sealed class IntVariable : BaseVariable<int>
    {
        /// <summary>
        /// Add two IntVariables together
        /// </summary>
        public static int operator +(IntVariable a, IntVariable b) => a.value + b.value;

        /// <summary>
        /// Subtract one IntVariable from another
        /// </summary>
        public static int operator -(IntVariable a, IntVariable b) => a.value - b.value;

        /// <summary>
        /// Implicitly convert an IntVariable to an int
        /// </summary>
        public static implicit operator int(IntVariable variable) => variable.value;
    }
}