namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Base class for banner advertisement implementations.
    /// Provides common functionality for banner ad display and hiding.
    /// </summary>
    public abstract class BaseBannerAd : BaseAd
    {
        /// <summary>
        /// Hides the banner advertisement.
        /// Must be implemented by derived classes.
        /// </summary>
        public abstract void HideAd();
    }
}