using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Base class for event channels that can raise events with one parameter.
    /// </summary>
    /// <typeparam name="T">The type of the parameter to pass when the event is raised.</typeparam>
    public abstract class EventChannel<T> : ScriptableObject
    {
        /// <summary>
        /// The event that is raised when Rai<PERSON> is called.
        /// </summary>
        public event UnityAction<T> OnEventRaised;

        /// <summary>
        /// Raises the event with the specified value.
        /// </summary>
        /// <param name="value">The value to pass to the event listeners.</param>
        public void Raise(T value)
        {
            OnEventRaised?.Invoke(value);
        }
    }

    /// <summary>
    /// Base class for event channels that can raise events with two parameters.
    /// </summary>
    /// <typeparam name="T1">The type of the first parameter.</typeparam>
    /// <typeparam name="T2">The type of the second parameter.</typeparam>
    public abstract class EventChannel<T1, T2> : ScriptableObject
    {
        /// <summary>
        /// The event that is raised when Rai<PERSON> is called.
        /// </summary>
        public event UnityAction<T1, T2> OnEventRaised;

        /// <summary>
        /// Raises the event with the specified values.
        /// </summary>
        /// <param name="value1">The first value to pass to the event listeners.</param>
        /// <param name="value2">The second value to pass to the event listeners.</param>
        public void Raise(T1 value1, T2 value2)
        {
            OnEventRaised?.Invoke(value1, value2);
        }
    }

    /// <summary>
    /// Base class for event channels that can raise events with three parameters.
    /// </summary>
    /// <typeparam name="T1">The type of the first parameter.</typeparam>
    /// <typeparam name="T2">The type of the second parameter.</typeparam>
    /// <typeparam name="T3">The type of the third parameter.</typeparam>
    public abstract class EventChannel<T1, T2, T3> : ScriptableObject
    {
        /// <summary>
        /// The event that is raised when Raise is called.
        /// </summary>
        public event UnityAction<T1, T2, T3> OnEventRaised;

        /// <summary>
        /// Raises the event with the specified values.
        /// </summary>
        /// <param name="value1">The first value to pass to the event listeners.</param>
        /// <param name="value2">The second value to pass to the event listeners.</param>
        /// <param name="value3">The third value to pass to the event listeners.</param>
        public void Raise(T1 value1, T2 value2, T3 value3)
        {
            OnEventRaised?.Invoke(value1, value2, value3);
        }
    }

    /// <summary>
    /// A mapping between an event channel and a Unity event with one parameter.
    /// </summary>
    /// <typeparam name="T">The type of the parameter.</typeparam>
    [Serializable]
    public class EventChannelMapping<T>
    {
        /// <summary>
        /// The event channel to listen to.
        /// </summary>
        [SerializeField]
        private EventChannel<T> eventChannel;

        /// <summary>
        /// Gets or sets the event channel to listen to.
        /// </summary>
        public EventChannel<T> EventChannel
        {
            get => eventChannel;
            set => eventChannel = value;
        }

        /// <summary>
        /// The Unity event to invoke when the event channel raises an event.
        /// </summary>
        [SerializeField]
        private UnityEvent<T> onEventRaised;

        /// <summary>
        /// Gets the Unity event to invoke when the event channel raises an event.
        /// </summary>
        public UnityEvent<T> OnEventRaised => onEventRaised;
    }

    /// <summary>
    /// A mapping between an event channel and a Unity event with two parameters.
    /// </summary>
    /// <typeparam name="T1">The type of the first parameter.</typeparam>
    /// <typeparam name="T2">The type of the second parameter.</typeparam>
    [Serializable]
    public class EventChannelMapping<T1, T2>
    {
        /// <summary>
        /// The event channel to listen to.
        /// </summary>
        [SerializeField]
        private EventChannel<T1, T2> eventChannel;

        /// <summary>
        /// Gets or sets the event channel to listen to.
        /// </summary>
        public EventChannel<T1, T2> EventChannel
        {
            get => eventChannel;
            set => eventChannel = value;
        }

        /// <summary>
        /// The Unity event to invoke when the event channel raises an event.
        /// </summary>
        [SerializeField]
        private UnityEvent<T1, T2> onEventRaised;

        /// <summary>
        /// Gets the Unity event to invoke when the event channel raises an event.
        /// </summary>
        public UnityEvent<T1, T2> OnEventRaised => onEventRaised;
    }

    /// <summary>
    /// A mapping between an event channel and a Unity event with three parameters.
    /// </summary>
    /// <typeparam name="T1">The type of the first parameter.</typeparam>
    /// <typeparam name="T2">The type of the second parameter.</typeparam>
    /// <typeparam name="T3">The type of the third parameter.</typeparam>
    [Serializable]
    public class EventChannelMapping<T1, T2, T3>
    {
        /// <summary>
        /// The event channel to listen to.
        /// </summary>
        [SerializeField]
        private EventChannel<T1, T2, T3> eventChannel;

        /// <summary>
        /// Gets or sets the event channel to listen to.
        /// </summary>
        public EventChannel<T1, T2, T3> EventChannel
        {
            get => eventChannel;
            set => eventChannel = value;
        }

        /// <summary>
        /// The Unity event to invoke when the event channel raises an event.
        /// </summary>
        [SerializeField]
        private UnityEvent<T1, T2, T3> onEventRaised;

        /// <summary>
        /// Gets the Unity event to invoke when the event channel raises an event.
        /// </summary>
        public UnityEvent<T1, T2, T3> OnEventRaised => onEventRaised;
    }

    /// <summary>
    /// Base class for event listeners that can listen to multiple event channels with one parameter.
    /// </summary>
    /// <typeparam name="T">The type of the parameter.</typeparam>
    public abstract class EventListener<T> : MonoBehaviour
    {
        /// <summary>
        /// The list of event channel mappings to listen to.
        /// </summary>
        [SerializeField]
        private List<EventChannelMapping<T>> eventChannelMappings = new();

        /// <summary>
        /// Gets the list of event channel mappings.
        /// </summary>
        public List<EventChannelMapping<T>> EventChannelMappings => eventChannelMappings;

        private void OnEnable()
        {
            SubscribeToEvents();
        }

        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }

        /// <summary>
        /// Subscribes to all event channels in the mappings list.
        /// </summary>
        protected virtual void SubscribeToEvents()
        {
            foreach (var mapping in eventChannelMappings)
            {
                if (mapping.EventChannel != null)
                {
                    mapping.EventChannel.OnEventRaised += mapping.OnEventRaised.Invoke;
                }
            }
        }

        /// <summary>
        /// Unsubscribes from all event channels in the mappings list.
        /// </summary>
        protected virtual void UnsubscribeFromEvents()
        {
            foreach (var mapping in eventChannelMappings)
            {
                if (mapping.EventChannel != null)
                {
                    mapping.EventChannel.OnEventRaised -= mapping.OnEventRaised.Invoke;
                }
            }
        }
    }

    /// <summary>
    /// Base class for event listeners that can listen to multiple event channels with two parameters.
    /// </summary>
    /// <typeparam name="T1">The type of the first parameter.</typeparam>
    /// <typeparam name="T2">The type of the second parameter.</typeparam>
    public abstract class EventListener<T1, T2> : MonoBehaviour
    {
        /// <summary>
        /// The list of event channel mappings to listen to.
        /// </summary>
        [SerializeField]
        private List<EventChannelMapping<T1, T2>> eventChannelMappings = new();

        /// <summary>
        /// Gets the list of event channel mappings.
        /// </summary>
        public List<EventChannelMapping<T1, T2>> EventChannelMappings => eventChannelMappings;

        private void OnEnable()
        {
            SubscribeToEvents();
        }

        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }

        /// <summary>
        /// Subscribes to all event channels in the mappings list.
        /// </summary>
        protected virtual void SubscribeToEvents()
        {
            foreach (var mapping in eventChannelMappings)
            {
                if (mapping.EventChannel != null)
                {
                    mapping.EventChannel.OnEventRaised += mapping.OnEventRaised.Invoke;
                }
            }
        }

        /// <summary>
        /// Unsubscribes from all event channels in the mappings list.
        /// </summary>
        protected virtual void UnsubscribeFromEvents()
        {
            foreach (var mapping in eventChannelMappings)
            {
                if (mapping.EventChannel != null)
                {
                    mapping.EventChannel.OnEventRaised -= mapping.OnEventRaised.Invoke;
                }
            }
        }
    }

    /// <summary>
    /// Base class for event listeners that can listen to multiple event channels with three parameters.
    /// </summary>
    /// <typeparam name="T1">The type of the first parameter.</typeparam>
    /// <typeparam name="T2">The type of the second parameter.</typeparam>
    /// <typeparam name="T3">The type of the third parameter.</typeparam>
    public abstract class EventListener<T1, T2, T3> : MonoBehaviour
    {
        /// <summary>
        /// The list of event channel mappings to listen to.
        /// </summary>
        [SerializeField]
        private List<EventChannelMapping<T1, T2, T3>> eventChannelMappings = new();

        /// <summary>
        /// Gets the list of event channel mappings.
        /// </summary>
        public List<EventChannelMapping<T1, T2, T3>> EventChannelMappings => eventChannelMappings;

        private void OnEnable()
        {
            SubscribeToEvents();
        }

        private void OnDisable()
        {
            UnsubscribeFromEvents();
        }

        /// <summary>
        /// Subscribes to all event channels in the mappings list.
        /// </summary>
        protected virtual void SubscribeToEvents()
        {
            foreach (var mapping in eventChannelMappings)
            {
                if (mapping.EventChannel != null)
                {
                    mapping.EventChannel.OnEventRaised += mapping.OnEventRaised.Invoke;
                }
            }
        }

        /// <summary>
        /// Unsubscribes from all event channels in the mappings list.
        /// </summary>
        protected virtual void UnsubscribeFromEvents()
        {
            foreach (var mapping in eventChannelMappings)
            {
                if (mapping.EventChannel != null)
                {
                    mapping.EventChannel.OnEventRaised -= mapping.OnEventRaised.Invoke;
                }
            }
        }
    }
}