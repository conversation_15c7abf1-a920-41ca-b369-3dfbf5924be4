Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit EnterpriseS' Language: 'en' Physical Memory: 12214 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-05T13:03:51Z

COMMAND LINE ARGUMENTS:
D:\Unity\Editors\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/Unity/Projects/Packages
-logFile
Logs/AssetImportWorker0.log
-srvPort
5820
-job-worker-count
1
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Unity/Projects/Packages
D:/Unity/Projects/Packages
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [4220]  Target information:

Player connection [4220]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 642584723 [EditorId] 642584723 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [4220]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 642584723 [EditorId] 642584723 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [4220]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 642584723 [EditorId] 642584723 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-HNCPQN5) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [4220] Host joined multi-casting on [***********:54997]...
Player connection [4220] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 1
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 6.23 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path D:/Unity/Editors/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Unity/Projects/Packages/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        Intel(R) HD Graphics 620 (ID=0x5916)
    Vendor:          Intel
    VRAM:            6107 MB
    App VRAM Budget: 5546 MB
    Driver:          31.0.101.2130
    Unified Memory Architecture
    Cache Coherent UMA
Initialize mono
Mono path[0] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Unity/Editors/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56000
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: D:/Unity/Editors/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.005042 seconds.
- Loaded All Assemblies, in  2.681 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 1355 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.442 seconds
Domain Reload Profiling: 6118ms
	BeginReloadAssembly (580ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (266ms)
	RebuildNativeTypeToScriptingClass (66ms)
	initialDomainReloadingComplete (426ms)
	LoadAllAssembliesAndSetupDomain (1336ms)
		LoadAssemblies (576ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1317ms)
			TypeCache.Refresh (1313ms)
				TypeCache.ScanAssembly (1244ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (3444ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3181ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1692ms)
			SetLoadedEditorAssemblies (98ms)
			BeforeProcessingInitializeOnLoad (503ms)
			ProcessInitializeOnLoadAttributes (575ms)
			ProcessInitializeOnLoadMethodAttributes (313ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.667 seconds
Refreshing native plugins compatible for Editor in 1.56 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.649 seconds
Domain Reload Profiling: 5314ms
	BeginReloadAssembly (411ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (86ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (1061ms)
		LoadAssemblies (711ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (586ms)
			TypeCache.Refresh (461ms)
				TypeCache.ScanAssembly (427ms)
			BuildScriptInfoCaches (98ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (3650ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3186ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (260ms)
			ProcessInitializeOnLoadAttributes (2549ms)
			ProcessInitializeOnLoadMethodAttributes (334ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.17 seconds
Refreshing native plugins compatible for Editor in 4.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 217 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6643 unused Assets / (4.9 MB). Loaded Objects now: 7358.
Memory consumption went from 166.7 MB to 161.9 MB.
Total: 28.437300 ms (FindLiveObjects: 1.561400 ms CreateObjectMapping: 2.351600 ms MarkObjects: 18.534000 ms  DeleteObjects: 5.987800 ms)

========================================================================
Received Import Request.
  Time since last request: 685485.625408 seconds.
  path: Assets/Tools
  artifactKey: Guid(b8a00357a11de9a42a97e6de223940f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools using Guid(b8a00357a11de9a42a97e6de223940f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7227e8fbf388d5bf4188f4d6d63c334b') in 0.0182117 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/InputSystem_Actions.inputactions
  artifactKey: Guid(052faaac586de48259a63d0c4782560b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem_Actions.inputactions using Guid(052faaac586de48259a63d0c4782560b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15be5ba4d5b2b3ffcc56475d7901d8b3') in 0.0800209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 99.234635 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/BoolEventChannel.cs
  artifactKey: Guid(30514a569bcee2f418661a798af38a5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/BoolEventChannel.cs using Guid(30514a569bcee2f418661a798af38a5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c2d0e87622b6bec88708ec84830bba5a') in 0.0007202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/VoidEventChannel.cs
  artifactKey: Guid(140d24b1fa873a64685b0f8b984b93b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/VoidEventChannel.cs using Guid(140d24b1fa873a64685b0f8b984b93b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b067ed83b7d8e633142c1f0d4ddefe3e') in 0.0007347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/Vector2EventChannel.cs
  artifactKey: Guid(fc3c2004ad47ecf47b9250568bfee67f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/Vector2EventChannel.cs using Guid(fc3c2004ad47ecf47b9250568bfee67f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dfeb84bc6163754df83095d9733f9a4f') in 0.0027108 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Tools/Runtime/Command/ICommand.cs
  artifactKey: Guid(164b6596a5348f24cb3f8df8b56544f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Command/ICommand.cs using Guid(164b6596a5348f24cb3f8df8b56544f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f092928451e5a183533495c563583e43') in 0.000791 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/BoolEventListener.cs
  artifactKey: Guid(30c94f32491e10f4ea2ecad3de1de1cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/BoolEventListener.cs using Guid(30c94f32491e10f4ea2ecad3de1de1cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dae9d1c14bd942b00a51acfc1e4c3cb6') in 0.0008485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectVariables/IntVariable.cs
  artifactKey: Guid(512330429c8319a459f8df102ead4a3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectVariables/IntVariable.cs using Guid(512330429c8319a459f8df102ead4a3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa95d3fa53fc92e7ed3fe7158d3e8a8b') in 0.0007658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Tools/Runtime/Testing/ToolsTestComponent.cs
  artifactKey: Guid(e88572660c8ab294690b4e0c2d91e185) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Testing/ToolsTestComponent.cs using Guid(e88572660c8ab294690b4e0c2d91e185) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '601b73c9f0a6af2b6156eba4ad0d122e') in 0.0007744 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Tools/Runtime/PrefabSpawner/PrefabSpawnerData.cs
  artifactKey: Guid(f2e698c95647625468f1dd774a7b955f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/PrefabSpawner/PrefabSpawnerData.cs using Guid(f2e698c95647625468f1dd774a7b955f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd24923a7e247f81317f7acbc6655e6e4') in 0.0022852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Tools/Editor/InterfaceReferenceDrawer.cs
  artifactKey: Guid(e7b2692924315cb458f9bdeaa70def17) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Editor/InterfaceReferenceDrawer.cs using Guid(e7b2692924315cb458f9bdeaa70def17) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3185f5f2129098bdf55b6f752649ffdb') in 0.0007311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectVariables/EmptyVariable.cs
  artifactKey: Guid(0890f67d7a7de1945ac7ab118d12b535) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectVariables/EmptyVariable.cs using Guid(0890f67d7a7de1945ac7ab118d12b535) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5b196d211f4e5b81a37c9bcd249ae86') in 0.0007412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Tools/Runtime/PrefabSpawner
  artifactKey: Guid(16f86c1d73a757e48b9bf992cb4f7c02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/PrefabSpawner using Guid(16f86c1d73a757e48b9bf992cb4f7c02) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cbd13fc8fe12b7099e89866b3bda7cf7') in 0.0009062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/TransformEventChannel.cs
  artifactKey: Guid(1a4b951f1dd466741bae3c5798d7e52d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/TransformEventChannel.cs using Guid(1a4b951f1dd466741bae3c5798d7e52d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ee2f2f1e3dee6331e63c8279c6a5105f') in 0.0011427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/FloatEventListener.cs
  artifactKey: Guid(7e76f1a684de05e4bb52ec4161de77cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/FloatEventListener.cs using Guid(7e76f1a684de05e4bb52ec4161de77cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '375fc2b335ed185bb50736e202f225dc') in 0.0010053 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Tools/Runtime/Singleton/Singleton.cs
  artifactKey: Guid(fabdcec7bb1929240b68509250e4caa8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Singleton/Singleton.cs using Guid(fabdcec7bb1929240b68509250e4caa8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '38d17891d4f2d0e37c26e6be66517e09') in 0.0008324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectVariables/BoolVariable.cs
  artifactKey: Guid(db040ecbe8ff93f4fab81554bed136f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectVariables/BoolVariable.cs using Guid(db040ecbe8ff93f4fab81554bed136f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '875603c4812abe2524b0b9bc5c8d5872') in 0.0006722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectVariables/StringVariable.cs
  artifactKey: Guid(eae983f932a2b8b4a928acc1adb85447) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectVariables/StringVariable.cs using Guid(eae983f932a2b8b4a928acc1adb85447) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '87809e0d530837224d265c9374ddda81') in 0.000814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Tools/Editor/EventBusWindow.cs
  artifactKey: Guid(f1a01d18664d6434a8af3ab57af5cbf9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Editor/EventBusWindow.cs using Guid(f1a01d18664d6434a8af3ab57af5cbf9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dab6f45e290b9f442e3008f131ebbb7a') in 0.0006814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/Vector2EventListener.cs
  artifactKey: Guid(4eb863e972b4d4e4bab0dd07e55810cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/Vector2EventListener.cs using Guid(4eb863e972b4d4e4bab0dd07e55810cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd5a26dcde119c1fb2ea0dc1d4c866683') in 0.0007316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/IntEventChannel.cs
  artifactKey: Guid(d693fc61549f58d4ba46d49f0ed5e6a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/IntEventChannel.cs using Guid(d693fc61549f58d4ba46d49f0ed5e6a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '826f9ef59dc586f347ca177a06e5473e') in 0.0007089 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Tools/Editor/ServiceLocatorWindow.cs
  artifactKey: Guid(777f211b2d4a9a6439c819fd79bd3456) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Editor/ServiceLocatorWindow.cs using Guid(777f211b2d4a9a6439c819fd79bd3456) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a62ff6d40a2f079da5cd37b2f1c99e08') in 0.0013977 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/IntEventListener.cs
  artifactKey: Guid(5098b9a2111fb874a883e7778e144c28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/IntEventListener.cs using Guid(5098b9a2111fb874a883e7778e144c28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4d8d29f5db42e4d09746c9e51205eaa7') in 0.0007438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/FloatEventChannel.cs
  artifactKey: Guid(cf2d013822bdb9340acb2a16532669f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/FloatEventChannel.cs using Guid(cf2d013822bdb9340acb2a16532669f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '65984953ae045e6a32ed8f781cc0749f') in 0.0008069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Tools/Runtime/ScriptableObjectEvents/BaseEvents.cs
  artifactKey: Guid(a537c2235db4df64a9de696d4eaa6d29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/ScriptableObjectEvents/BaseEvents.cs using Guid(a537c2235db4df64a9de696d4eaa6d29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '75eac762044e30b0480255c7145fa362') in 0.0007489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

