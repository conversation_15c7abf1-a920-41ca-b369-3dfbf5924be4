using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Detects collision events and exposes them via UnityEvents.
    /// </summary>
    public class CollisionDetection : MonoBehaviour
    {
        [SerializeField]
        private UnityEvent<Collision> onCollisionEnterEvent = new UnityEvent<Collision>();
        [SerializeField]
        private UnityEvent<Collision> onCollisionExitEvent = new UnityEvent<Collision>();
        [SerializeField]
        private UnityEvent<Collision> onCollisionStayEvent = new UnityEvent<Collision>();

        /// <summary>
        /// Invoked when a collision starts.
        /// </summary>
        public UnityEvent<Collision> OnCollisionEnterEvent => onCollisionEnterEvent;
        /// <summary>
        /// Invoked when a collision ends.
        /// </summary>
        public UnityEvent<Collision> OnCollisionExitEvent => onCollisionExitEvent;
        /// <summary>
        /// Invoked while a collision persists.
        /// </summary>
        public UnityEvent<Collision> OnCollisionStayEvent => onCollisionStayEvent;

        private void OnCollisionEnter(Collision collision)
        {
            onCollisionEnterEvent.Invoke(collision);
        }

        private void OnCollisionExit(Collision collision)
        {
            onCollisionExitEvent.Invoke(collision);
        }

        private void OnCollisionStay(Collision collision)
        {
            onCollisionStayEvent.Invoke(collision);
        }
    }
}