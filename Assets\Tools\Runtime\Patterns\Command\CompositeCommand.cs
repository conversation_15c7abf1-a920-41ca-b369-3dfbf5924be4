using System.Collections.Generic;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Represents a command composed of multiple child commands, executed and undone as a unit.
    /// </summary>
    public class CompositeCommand : ICommand
    {
        private readonly List<ICommand> commands;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompositeCommand"/> class with the specified commands.
        /// </summary>
        /// <param name="commands">The commands to compose and execute as a group.</param>
        public CompositeCommand(IEnumerable<ICommand> commands)
        {
            this.commands = new List<ICommand>(commands);
        }

        /// <summary>
        /// Executes all child commands in order.
        /// </summary>
        public void Execute()
        {
            foreach (var command in commands)
            {
                command.Execute();
            }
        }

        /// <summary>
        /// Undoes all child commands in reverse order.
        /// </summary>
        public void Undo()
        {
            for (int i = commands.Count - 1; i >= 0; i--)
            {
                commands[i].Undo();
            }
        }
    }
}
