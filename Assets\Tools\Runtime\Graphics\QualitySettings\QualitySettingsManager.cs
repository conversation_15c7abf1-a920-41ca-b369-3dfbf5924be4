using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Manages quality settings and auto-detection based on hardware.
    /// </summary>
    public class QualitySettingsManager : Singleton<QualitySettingsManager>
    {
        [Tooltip("Define available quality settings profiles (in descending order by desired quality).")]
        [SerializeField] private QualitySettingsData[] availableQualitySettings;
        [Tooltip("Fallback quality index if no suitable profile is found.")]
        [SerializeField] private int fallbackQualityIndex = 0;
        [SerializeField] private bool autoDetectOnStart = true;
        [SerializeField] private bool enableLogs = true;

        /// <summary>
        /// Invoked when the quality level changes.
        /// </summary>
        public UnityEvent<QualityIndex> OnQualityLevelChanged => onQualityLevelChanged;
        [SerializeField] private UnityEvent<QualityIndex> onQualityLevelChanged = new UnityEvent<QualityIndex>();

        /// <summary>
        /// PlayerPrefs key for quality level.
        /// </summary>
        public const string QualityLevelKey = "QualityLevel";

        private void Log(string message)
        {
            if (enableLogs)
                Debug.Log(message);
        }

        private void Start()
        {
            if (PlayerPrefs.HasKey(QualityLevelKey))
            {
                int qualityIndex = PlayerPrefs.GetInt(QualityLevelKey);
                SetQuality(qualityIndex);
                Log($"Quality level loaded from PlayerPrefs: {qualityIndex}.");
                return;
            }
            if (autoDetectOnStart)
                AutoDetectQualitySettings();
        }

        /// <summary>
        /// Sets the quality level by index.
        /// </summary>
        /// <param name="qualityIndex">Quality level index.</param>
        public void SetQuality(int qualityIndex)
        {
            QualitySettings.SetQualityLevel(qualityIndex, true);
            Log($"Quality level set to {qualityIndex}.");
            onQualityLevelChanged?.Invoke((QualityIndex)qualityIndex);
        }

        /// <summary>
        /// Sets the quality level by enum.
        /// </summary>
        /// <param name="qualityIndex">Quality index enum.</param>
        public void SetQuality(QualityIndex qualityIndex)
        {
            int index = (int)qualityIndex;
            QualitySettings.SetQualityLevel(index, true);
            Log($"Quality level set to {index} ({qualityIndex}).");
            onQualityLevelChanged?.Invoke(qualityIndex);
        }

        /// <summary>
        /// Automatically detects and sets the best quality settings based on hardware.
        /// </summary>
        public void AutoDetectQualitySettings()
        {
            int qualityLevel = 0;

            int graphicsMemory = SystemInfo.graphicsMemorySize;
            int systemMemory = SystemInfo.systemMemorySize;
            int processorCount = SystemInfo.processorCount;
            int processorFrequency = SystemInfo.processorFrequency;
            DeviceType deviceType = SystemInfo.deviceType;
            string operatingSystem = SystemInfo.operatingSystem;
            string deviceModel = SystemInfo.deviceModel;
            string graphicsDeviceName = SystemInfo.graphicsDeviceName;
            GraphicsDeviceType graphicsDeviceType = SystemInfo.graphicsDeviceType;
            int graphicsShaderLevel = SystemInfo.graphicsShaderLevel;
            int supportedRenderTargetCount = SystemInfo.supportedRenderTargetCount;

            Log("Hardware details:" +
                $"\nOperating System: {operatingSystem}" +
                $"\nDevice Model: {deviceModel}" +
                $"\nDevice Type: {deviceType}" +
                $"\nSystem Memory: {systemMemory} MB" +
                $"\nGraphics Memory: {graphicsMemory} MB" +
                $"\nProcessor: {processorCount} cores at {processorFrequency} MHz" +
                $"\nGraphics Device: {graphicsDeviceName} ({graphicsDeviceType})" +
                $"\nGraphics Shader Level: {graphicsShaderLevel}" +
                $"\nSupported Render Target Count: {supportedRenderTargetCount}");

            QualitySettingsData bestMatch = null;

            if (availableQualitySettings != null && availableQualitySettings.Length > 0)
            {
                foreach (var setting in availableQualitySettings)
                {
                    if (systemMemory >= setting.systemMemory &&
                        graphicsMemory >= setting.graphicsMemory &&
                        processorCount >= setting.processorCount &&
                        processorFrequency >= setting.processorFrequency)
                    {
                        if (bestMatch == null || (int)setting.qualityIndex > (int)bestMatch.qualityIndex)
                        {
                            bestMatch = setting;
                        }
                    }
                }

                if (bestMatch != null)
                {
                    qualityLevel = (int)bestMatch.qualityIndex;
                    Log($"Best matched settings: Quality {bestMatch.qualityIndex} (Thresholds - System: {bestMatch.systemMemory}MB, Graphics: {bestMatch.graphicsMemory}MB, Cores: {bestMatch.processorCount}, Frequency: {bestMatch.processorFrequency}MHz)");
                }
                else
                {
                    qualityLevel = fallbackQualityIndex;
                    Log("No matching quality settings found. Using fallback quality index.");
                }
            }
            else
            {
                qualityLevel = fallbackQualityIndex;
                Log("No available quality settings profiles found. Using fallback quality index.");
            }

            SetQuality(qualityLevel);
            onQualityLevelChanged?.Invoke((QualityIndex)qualityLevel);
        }
    }
}