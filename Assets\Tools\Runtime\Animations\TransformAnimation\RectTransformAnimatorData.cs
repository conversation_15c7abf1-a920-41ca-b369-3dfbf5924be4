using System;
using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Enum defining the RectTransform property to animate
    /// </summary>
    public enum RectTransformProperty
    {
        /// <summary>
        /// Animate the anchored position of the RectTransform (Vector2)
        /// </summary>
        AnchoredPosition,

        /// <summary>
        /// Animate the size delta of the RectTransform (Vector2)
        /// </summary>
        Si<PERSON><PERSON>el<PERSON>,

        /// <summary>
        /// Animate the anchor min of the RectTransform (Vector2)
        /// </summary>
        AnchorMin,

        /// <summary>
        /// Animate the anchor max of the RectTransform (Vector2)
        /// </summary>
        AnchorMax,

        /// <summary>
        /// Animate the pivot of the RectTransform (Vector2)
        /// </summary>
        Pivot,

        /// <summary>
        /// Animate the position of the transform (Vector3)
        /// </summary>
        Position,

        /// <summary>
        /// Animate the rotation of the transform (stored as Euler angles but interpolated using Quaternions)
        /// </summary>
        Rotation,

        /// <summary>
        /// Animate the scale of the transform (Vector3)
        /// </summary>
        Scale
    }

    /// <summary>
    /// Defines a single RectTransform animation mode
    /// </summary>
    [Serializable]
    public class RectTransformAnimationMode
    {
        /// <summary>
        /// The RectTransform property to animate
        /// </summary>
        [Tooltip("The RectTransform property to animate")]
        public RectTransformProperty property;

        /// <summary>
        /// Whether to use local or global space (only applies to Position, Rotation, Scale)
        /// </summary>
        [Tooltip("Whether to use local or global space (only applies to Position, Rotation, Scale)")]
        public bool useLocalSpace = true;

        /// <summary>
        /// How to apply the target value
        /// </summary>
        [Tooltip("Whether to set the value absolutely or add it incrementally")]
        public ValueApplicationMode applicationMode = ValueApplicationMode.Absolute;

        /// <summary>
        /// The target Vector2 value for the animation (used for AnchoredPosition, SizeDelta, AnchorMin, AnchorMax, Pivot)
        /// </summary>
        [Tooltip("The target Vector2 value for the animation")]
        public Vector2 targetVector2Value;

        /// <summary>
        /// The target Vector3 value for the animation (used for Position, Rotation, Scale)
        /// </summary>
        [Tooltip("The target Vector3 value for the animation")]
        public Vector3 targetVector3Value;
    }

    /// <summary>
    /// Defines a step in the RectTransform animation sequence that can contain multiple animation modes
    /// </summary>
    [Serializable]
    public class RectTransformAnimationStep
    {
        /// <summary>
        /// Array of animation modes to play simultaneously in this step
        /// </summary>
        [Tooltip("Animation modes to play simultaneously")]
        public RectTransformAnimationMode[] animationModes;

        /// <summary>
        /// Duration of this animation step in seconds
        /// </summary>
        [Tooltip("Duration of this animation step in seconds")]
        public float duration = 1f;

        /// <summary>
        /// Animation curve to control the interpolation
        /// </summary>
        [Tooltip("Animation curve to control the interpolation")]
        public AnimationCurve animationCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
    }

    /// <summary>
    /// ScriptableObject that defines a complete RectTransform animation sequence
    /// </summary>
    [CreateAssetMenu(fileName = "RectTransformAnimatorData", menuName = "Scriptable Objects/Transform Animator/RectTransform Animator Data")]
    public class RectTransformAnimatorData : ScriptableObject
    {
        /// <summary>
        /// The update mode to use for this animation
        /// </summary>
        [Tooltip("The update mode to use for this animation")]
        public TransformUpdateMode updateMode = TransformUpdateMode.Update;

        /// <summary>
        /// Whether to loop the animation
        /// </summary>
        [Tooltip("Whether to loop the animation")]
        public bool loop = false;

        /// <summary>
        /// Whether to play the animation when the GameObject is enabled
        /// </summary>
        [Tooltip("Whether to play the animation when the GameObject is enabled")]
        public bool playOnEnable = false;

        /// <summary>
        /// Array of animation steps to play sequentially
        /// </summary>
        [Tooltip("Animation steps to play sequentially")]
        public RectTransformAnimationStep[] animationSteps;
    }
}
