using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// MonoBehaviour that executes RectTransform animations based on RectTransformAnimationData
    /// </summary>
    public class RectTransformAnimator : MonoBehaviour
    {
        /// <summary>
        /// The animation data to use
        /// </summary>
        [Tooltip("The animation data to use")]
        [SerializeField] private RectTransformAnimatorData animatorData;

        /// <summary>
        /// The RectTransform to animate (defaults to this GameObject's RectTransform)
        /// </summary>
        [Tooltip("The RectTransform to animate (defaults to this GameObject's RectTransform)")]
        [SerializeField] private RectTransform targetRectTransform;

        /// <summary>
        /// Event triggered when the animation starts
        /// </summary>
        [Tooltip("Event triggered when the animation starts")]
        [SerializeField] private UnityEvent animationStartEvent;

        /// <summary>
        /// Event triggered when the animation completes
        /// </summary>
        [Tooltip("Event triggered when the animation completes")]
        [SerializeField] private UnityEvent animationCompleteEvent;

        /// <summary>
        /// Event triggered when a step in the animation completes
        /// </summary>
        [Tooltip("Event triggered when a step in the animation completes")]
        [SerializeField] private UnityEvent<int> stepCompleteEvent;

        private bool isPlaying = false;
        private int currentStepIndex = 0;
        private float currentStepTime = 0f;

        /// <summary>
        /// Gets or sets the animation data
        /// </summary>
        public RectTransformAnimatorData AnimationData
        {
            get => animatorData;
            set => animatorData = value;
        }

        /// <summary>
        /// Gets or sets the target RectTransform
        /// </summary>
        public RectTransform TargetRectTransform
        {
            get => targetRectTransform;
            set => targetRectTransform = value;
        }

        /// <summary>
        /// Gets or sets the event triggered when the animation starts
        /// </summary>
        public UnityEvent OnAnimationStart
        {
            get => animationStartEvent;
            set => animationStartEvent = value;
        }

        /// <summary>
        /// Gets or sets the event triggered when the animation completes
        /// </summary>
        public UnityEvent OnAnimationComplete
        {
            get => animationCompleteEvent;
            set => animationCompleteEvent = value;
        }

        /// <summary>
        /// Gets or sets the event triggered when a step in the animation completes
        /// </summary>
        public UnityEvent<int> OnStepComplete
        {
            get => stepCompleteEvent;
            set => stepCompleteEvent = value;
        }

        /// <summary>
        /// Gets whether the animation is currently playing
        /// </summary>
        public bool IsPlaying => isPlaying;

        /// <summary>
        /// Gets the current step index
        /// </summary>
        public int CurrentStepIndex => currentStepIndex;

        private void Awake()
        {
            // If no target RectTransform is specified, use this GameObject's RectTransform
            if (targetRectTransform == null)
            {
                targetRectTransform = GetComponent<RectTransform>();
            }
        }

        private void OnEnable()
        {
            if (animatorData != null && animatorData.playOnEnable)
            {
                Play();
            }
        }

        private void Update()
        {
            if (isPlaying && animatorData != null && animatorData.updateMode == TransformUpdateMode.Update)
            {
                UpdateAnimation(Time.deltaTime);
            }
        }

        private void FixedUpdate()
        {
            if (isPlaying && animatorData != null && animatorData.updateMode == TransformUpdateMode.FixedUpdate)
            {
                UpdateAnimation(Time.fixedDeltaTime);
            }
        }

        private void LateUpdate()
        {
            if (isPlaying && animatorData != null && animatorData.updateMode == TransformUpdateMode.LateUpdate)
            {
                UpdateAnimation(Time.deltaTime);
            }
        }

        /// <summary>
        /// Plays the animation from the beginning
        /// </summary>
        public void Play()
        {
            if (animatorData == null || animatorData.animationSteps == null || animatorData.animationSteps.Length == 0)
            {
                Debug.LogWarning("Cannot play animation: Animation data is null or has no steps.");
                return;
            }

            Stop();
            isPlaying = true;
            currentStepIndex = 0;
            currentStepTime = 0f;
            animationStartEvent?.Invoke();
        }

        /// <summary>
        /// Pauses the animation
        /// </summary>
        public void Pause()
        {
            isPlaying = false;
        }

        /// <summary>
        /// Resumes the animation from where it was paused
        /// </summary>
        public void Resume()
        {
            if (animatorData == null)
            {
                Debug.LogWarning("Cannot resume animation: Animation data is null.");
                return;
            }

            isPlaying = true;
        }

        /// <summary>
        /// Stops the animation and resets its state
        /// </summary>
        public void Stop()
        {
            isPlaying = false;
            currentStepIndex = 0;
            currentStepTime = 0f;
            initialValues.Clear();
        }

        // Store initial values for each animation mode
        private readonly Dictionary<RectTransformAnimationMode, object> initialValues = new();

        private void UpdateAnimation(float deltaTime)
        {
            if (animatorData == null || animatorData.animationSteps == null ||
                currentStepIndex >= animatorData.animationSteps.Length || !isPlaying)
            {
                return;
            }

            RectTransformAnimationStep currentStep = animatorData.animationSteps[currentStepIndex];

            // If this is the first frame of this step, capture initial values
            if (currentStepTime == 0f)
            {
                CaptureInitialValues(currentStep);
            }

            currentStepTime += deltaTime;

            // Calculate normalized time (0 to 1) and apply animation curve
            float normalizedTime = Mathf.Clamp01(currentStepTime / currentStep.duration);
            float curveValue = currentStep.animationCurve.Evaluate(normalizedTime);

            // Apply all animation modes in the current step
            ApplyAnimationStep(currentStep, curveValue);

            // Check if the current step is complete
            if (normalizedTime >= 1f)
            {
                stepCompleteEvent?.Invoke(currentStepIndex);
                currentStepIndex++;
                currentStepTime = 0f;

                // Check if the entire animation is complete
                if (currentStepIndex >= animatorData.animationSteps.Length)
                {
                    if (animatorData.loop)
                    {
                        // Loop back to the beginning
                        currentStepIndex = 0;
                    }
                    else
                    {
                        // Animation is complete
                        isPlaying = false;
                        animationCompleteEvent?.Invoke();
                    }
                }
            }
        }

        private void ApplyAnimationStep(RectTransformAnimationStep step, float t)
        {
            if (step.animationModes == null || targetRectTransform == null)
            {
                return;
            }

            foreach (RectTransformAnimationMode mode in step.animationModes)
            {
                ApplyAnimationMode(mode, t);
            }
        }

        private void CaptureInitialValues(RectTransformAnimationStep step)
        {
            initialValues.Clear();

            if (step.animationModes == null || targetRectTransform == null)
            {
                return;
            }

            foreach (RectTransformAnimationMode mode in step.animationModes)
            {
                // Capture current RectTransform value
                switch (mode.property)
                {
                    case RectTransformProperty.AnchoredPosition:
                        initialValues[mode] = targetRectTransform.anchoredPosition;
                        break;
                    case RectTransformProperty.SizeDelta:
                        initialValues[mode] = targetRectTransform.sizeDelta;
                        break;
                    case RectTransformProperty.AnchorMin:
                        initialValues[mode] = targetRectTransform.anchorMin;
                        break;
                    case RectTransformProperty.AnchorMax:
                        initialValues[mode] = targetRectTransform.anchorMax;
                        break;
                    case RectTransformProperty.Pivot:
                        initialValues[mode] = targetRectTransform.pivot;
                        break;
                    case RectTransformProperty.Position:
                        Vector3 posValue = mode.useLocalSpace ? targetRectTransform.localPosition : targetRectTransform.position;
                        initialValues[mode] = posValue;
                        break;
                    case RectTransformProperty.Rotation:
                        Quaternion rotValue = mode.useLocalSpace ? targetRectTransform.localRotation : targetRectTransform.rotation;
                        initialValues[mode] = rotValue;
                        break;
                    case RectTransformProperty.Scale:
                        Vector3 scaleValue = targetRectTransform.localScale;
                        initialValues[mode] = scaleValue;
                        break;
                }
            }
        }

        private void ApplyAnimationMode(RectTransformAnimationMode mode, float t)
        {
            // Apply the animation based on property type
            switch (mode.property)
            {
                case RectTransformProperty.AnchoredPosition:
                    ApplyAnchoredPositionAnimation(mode, t);
                    break;

                case RectTransformProperty.SizeDelta:
                    ApplySizeDeltaAnimation(mode, t);
                    break;

                case RectTransformProperty.AnchorMin:
                    ApplyAnchorMinAnimation(mode, t);
                    break;

                case RectTransformProperty.AnchorMax:
                    ApplyAnchorMaxAnimation(mode, t);
                    break;

                case RectTransformProperty.Pivot:
                    ApplyPivotAnimation(mode, t);
                    break;

                case RectTransformProperty.Position:
                    ApplyPositionAnimation(mode, t);
                    break;

                case RectTransformProperty.Rotation:
                    ApplyRotationAnimation(mode, t);
                    break;

                case RectTransformProperty.Scale:
                    ApplyScaleAnimation(mode, t);
                    break;
            }
        }

        private void ApplyAnchoredPositionAnimation(RectTransformAnimationMode mode, float t)
        {
            // Get the initial anchored position value
            Vector2 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector2 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = targetRectTransform.anchoredPosition;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector2 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetVector2Value;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetVector2Value;
            }

            // Apply the anchored position animation
            targetRectTransform.anchoredPosition = Vector2.Lerp(startValue, finalTargetValue, t);
        }

        private void ApplySizeDeltaAnimation(RectTransformAnimationMode mode, float t)
        {
            // Get the initial size delta value
            Vector2 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector2 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = targetRectTransform.sizeDelta;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector2 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetVector2Value;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetVector2Value;
            }

            // Apply the size delta animation
            targetRectTransform.sizeDelta = Vector2.Lerp(startValue, finalTargetValue, t);
        }

        private void ApplyAnchorMinAnimation(RectTransformAnimationMode mode, float t)
        {
            // Get the initial anchor min value
            Vector2 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector2 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = targetRectTransform.anchorMin;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector2 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetVector2Value;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetVector2Value;
            }

            // Apply the anchor min animation
            targetRectTransform.anchorMin = Vector2.Lerp(startValue, finalTargetValue, t);
        }

        private void ApplyAnchorMaxAnimation(RectTransformAnimationMode mode, float t)
        {
            // Get the initial anchor max value
            Vector2 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector2 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = targetRectTransform.anchorMax;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector2 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetVector2Value;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetVector2Value;
            }

            // Apply the anchor max animation
            targetRectTransform.anchorMax = Vector2.Lerp(startValue, finalTargetValue, t);
        }

        private void ApplyPivotAnimation(RectTransformAnimationMode mode, float t)
        {
            // Get the initial pivot value
            Vector2 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector2 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = targetRectTransform.pivot;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector2 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetVector2Value;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetVector2Value;
            }

            // Apply the pivot animation
            targetRectTransform.pivot = Vector2.Lerp(startValue, finalTargetValue, t);
        }

        private void ApplyPositionAnimation(RectTransformAnimationMode mode, float t)
        {
            // Get the initial position value
            Vector3 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector3 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = mode.useLocalSpace ? targetRectTransform.localPosition : targetRectTransform.position;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector3 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetVector3Value;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetVector3Value;
            }

            // Apply the position animation
            if (mode.useLocalSpace)
            {
                targetRectTransform.localPosition = Vector3.Lerp(startValue, finalTargetValue, t);
            }
            else
            {
                targetRectTransform.position = Vector3.Lerp(startValue, finalTargetValue, t);
            }
        }

        private void ApplyRotationAnimation(RectTransformAnimationMode mode, float t)
        {
            // Get the initial rotation value
            Quaternion startRotation;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Quaternion initialQuaternion)
            {
                startRotation = initialQuaternion;
            }
            else
            {
                // If not found, use current value
                startRotation = mode.useLocalSpace ? targetRectTransform.localRotation : targetRectTransform.rotation;
                initialValues[mode] = startRotation;
            }

            // Calculate the target rotation based on application mode
            Quaternion finalTargetRotation;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the Euler angles to the initial rotation
                Quaternion additionalRotation = Quaternion.Euler(mode.targetVector3Value);
                finalTargetRotation = startRotation * additionalRotation;
            }
            else
            {
                // For absolute mode, use the target Euler angles directly
                finalTargetRotation = Quaternion.Euler(mode.targetVector3Value);
            }

            // Apply the rotation animation using Quaternion.Slerp to avoid gimbal lock
            // Quaternion.Slerp provides smooth spherical interpolation between rotations,
            // which is more natural and avoids the issues that can occur with Euler angle interpolation
            if (mode.useLocalSpace)
            {
                targetRectTransform.localRotation = Quaternion.Slerp(startRotation, finalTargetRotation, t);
            }
            else
            {
                targetRectTransform.rotation = Quaternion.Slerp(startRotation, finalTargetRotation, t);
            }
        }

        private void ApplyScaleAnimation(RectTransformAnimationMode mode, float t)
        {
            // Get the initial scale value
            Vector3 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector3 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = targetRectTransform.localScale;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector3 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetVector3Value;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetVector3Value;
            }

            // Scale is always local
            targetRectTransform.localScale = Vector3.Lerp(startValue, finalTargetValue, t);
        }

        /// <summary>
        /// Plays a specific animation step
        /// </summary>
        /// <param name="stepIndex">The index of the step to play</param>
        public void PlayStep(int stepIndex)
        {
            if (animatorData == null || animatorData.animationSteps == null ||
                stepIndex < 0 || stepIndex >= animatorData.animationSteps.Length)
            {
                Debug.LogWarning($"Cannot play step {stepIndex}: Invalid step index.");
                return;
            }

            initialValues.Clear();
            currentStepIndex = stepIndex;
            currentStepTime = 0f;
            isPlaying = true;

            // Capture initial values for this step
            CaptureInitialValues(animatorData.animationSteps[stepIndex]);
        }
    }
}
