using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Centralized manager for displaying advertisements from multiple providers.
    /// Handles banner, interstitial, and rewarded ads with provider failover.
    /// </summary>
    public class AdManager : Singleton<AdManager>
    {
        #region Serialized Fields

        [Header("Banner Ads")]
        [SerializeField] private List<BaseBannerAd> bannerProviders = new();
        [SerializeField] private UnityEvent onBannerAdLoaded = new();
        [SerializeField] private UnityEvent onBannerAdImpression = new();
        [SerializeField] private UnityEvent onBannerAdClick = new();
        [SerializeField] private UnityEvent<string> onBannerAdError = new();

        [Header("Interstitial Ads")]
        [SerializeField] private List<BaseInterstitialAd> interstitialProviders = new();
        [SerializeField] private UnityEvent onInterstitialAdLoaded = new();
        [SerializeField] private UnityEvent onInterstitialAdImpression = new();
        [SerializeField] private UnityEvent onInterstitialAdClick = new();
        [SerializeField] private UnityEvent<string> onInterstitialAdError = new();

        [Header("Rewarded Ads")]
        [SerializeField] private List<BaseRewardedAd> rewardedProviders = new();
        [SerializeField] private UnityEvent onRewardedAdLoaded = new();
        [SerializeField] private UnityEvent onRewardedAdImpression = new();
        [SerializeField] private UnityEvent onRewardedAdClick = new();
        [SerializeField] private UnityEvent<string> onRewardedAdError = new();
        [SerializeField] private UnityEvent onUserEarnedRewardedAd = new();

        [Header("Provider Selection")]
        [SerializeField] private bool useRandomProviderSelection = false;

        #endregion

        #region Private Fields

        // Currently active providers
        private BaseBannerAd currentBannerProvider;
        private BaseInterstitialAd currentInterstitialProvider;
        private BaseRewardedAd currentRewardedProvider;

        // Lists of providers that have been tried in the current ad request
        private readonly List<BaseBannerAd> triedBannerProviders = new();
        private readonly List<BaseInterstitialAd> triedInterstitialProviders = new();
        private readonly List<BaseRewardedAd> triedRewardedProviders = new();

        #endregion

        #region Public Events

        /// <summary>
        /// Event triggered when a banner ad is loaded.
        /// </summary>
        public UnityEvent OnBannerAdLoaded => onBannerAdLoaded;

        /// <summary>
        /// Event triggered when a banner ad impression is recorded.
        /// </summary>
        public UnityEvent OnBannerAdImpression => onBannerAdImpression;

        /// <summary>
        /// Event triggered when a banner ad is clicked.
        /// </summary>
        public UnityEvent OnBannerAdClick => onBannerAdClick;

        /// <summary>
        /// Event triggered when a banner ad error occurs.
        /// </summary>
        public UnityEvent<string> OnBannerAdError => onBannerAdError;

        /// <summary>
        /// Event triggered when an interstitial ad is loaded.
        /// </summary>
        public UnityEvent OnInterstitialAdLoaded => onInterstitialAdLoaded;

        /// <summary>
        /// Event triggered when an interstitial ad impression is recorded.
        /// </summary>
        public UnityEvent OnInterstitialAdImpression => onInterstitialAdImpression;

        /// <summary>
        /// Event triggered when an interstitial ad is clicked.
        /// </summary>
        public UnityEvent OnInterstitialAdClick => onInterstitialAdClick;

        /// <summary>
        /// Event triggered when an interstitial ad error occurs.
        /// </summary>
        public UnityEvent<string> OnInterstitialAdError => onInterstitialAdError;

        /// <summary>
        /// Event triggered when a rewarded ad is loaded.
        /// </summary>
        public UnityEvent OnRewardedAdLoaded => onRewardedAdLoaded;

        /// <summary>
        /// Event triggered when a rewarded ad impression is recorded.
        /// </summary>
        public UnityEvent OnRewardedAdImpression => onRewardedAdImpression;

        /// <summary>
        /// Event triggered when a rewarded ad is clicked.
        /// </summary>
        public UnityEvent OnRewardedAdClick => onRewardedAdClick;

        /// <summary>
        /// Event triggered when a rewarded ad error occurs.
        /// </summary>
        public UnityEvent<string> OnRewardedAdError => onRewardedAdError;

        /// <summary>
        /// Event triggered when a user earns a reward from watching a rewarded ad.
        /// </summary>
        public UnityEvent OnUserEarnedRewardedAd => onUserEarnedRewardedAd;

        #endregion

        /// <summary>
        /// Called when the script instance is being loaded.
        /// Initializes and loads all ad providers.
        /// </summary>
        protected override void Awake()
        {
            base.Awake();

            // Initialize all ad providers
            InitializeAllProviders();

            // Load all ad providers
            LoadAllProviders();
        }

        /// <summary>
        /// Called when the MonoBehaviour will be destroyed.
        /// Ensures proper cleanup of event subscriptions.
        /// </summary>
        protected override void OnDestroy()
        {
            // Clean up any active event subscriptions
            UnsubscribeFromBannerProviderEvents(currentBannerProvider);
            currentBannerProvider = null;

            UnsubscribeFromInterstitialProviderEvents(currentInterstitialProvider);
            currentInterstitialProvider = null;

            UnsubscribeFromRewardedProviderEvents(currentRewardedProvider);
            currentRewardedProvider = null;

            base.OnDestroy();
        }

        /// <summary>
        /// Shows a banner advertisement using the configured providers.
        /// </summary>
        public void ShowBannerAd()
        {
            // Reset the list of tried providers
            triedBannerProviders.Clear();

            // Select and show the first provider
            SelectNextBannerProvider();
        }

        /// <summary>
        /// Hides the currently displayed banner advertisement.
        /// </summary>
        public void HideBannerAd()
        {
            // Unsubscribe from events before hiding
            UnsubscribeFromBannerProviderEvents(currentBannerProvider);

            // Hide the banner ad if provider exists
            if (currentBannerProvider != null)
            {
                currentBannerProvider.HideAd();
                // Set current provider to null to indicate no banner is showing
                currentBannerProvider = null;
            }
        }

        /// <summary>
        /// Shows an interstitial advertisement using the configured providers.
        /// </summary>
        public void ShowInterstitialAd()
        {
            // Reset the list of tried providers
            triedInterstitialProviders.Clear();

            // Select and show the first provider
            SelectNextInterstitialProvider();
        }

        /// <summary>
        /// Shows a rewarded advertisement using the configured providers.
        /// </summary>
        public void ShowRewardedAd()
        {
            // Reset the list of tried providers
            triedRewardedProviders.Clear();

            // Select and show the first provider
            SelectNextRewardedProvider();
        }

        #region Banner Ad Methods

        /// <summary>
        /// Selects the next banner ad provider based on the configured selection strategy.
        /// </summary>
        private void SelectNextBannerProvider()
        {
            // Unsubscribe from previous provider events
            UnsubscribeFromBannerProviderEvents(currentBannerProvider);

            // Get the next provider
            currentBannerProvider = GetNextProvider(bannerProviders, triedBannerProviders);

            // If no provider is available, log a warning and return
            if (currentBannerProvider == null)
            {
                Debug.LogWarning("No available banner ad providers.");
                return;
            }

            // Subscribe to the provider's events
            SubscribeToBannerProviderEvents(currentBannerProvider);

            // Show the ad - the provider will handle CanShowAd checks internally
            currentBannerProvider.ShowAd();
        }

        /// <summary>
        /// Subscribes to the events of a banner ad provider.
        /// </summary>
        private void SubscribeToBannerProviderEvents(BaseBannerAd provider)
        {
            provider.OnMaxRetryAttemptsReached.AddListener(OnBannerProviderMaxRetryReached);
            provider.OnAdImpression.AddListener(HandleBannerAdImpression);
            provider.OnAdClick.AddListener(HandleBannerAdClick);
            provider.OnAdLoad.AddListener(HandleBannerAdLoaded);
            provider.OnAdError.AddListener(HandleBannerAdError);
        }

        /// <summary>
        /// Unsubscribes from the events of a banner ad provider.
        /// </summary>
        private void UnsubscribeFromBannerProviderEvents(BaseBannerAd provider)
        {
            if (provider == null)
                return;

            provider.OnMaxRetryAttemptsReached.RemoveListener(OnBannerProviderMaxRetryReached);
            provider.OnAdImpression.RemoveListener(HandleBannerAdImpression);
            provider.OnAdClick.RemoveListener(HandleBannerAdClick);
            provider.OnAdLoad.RemoveListener(HandleBannerAdLoaded);
            provider.OnAdError.RemoveListener(HandleBannerAdError);
        }

        /// <summary>
        /// Called when a banner ad provider reaches its maximum retry attempts.
        /// </summary>
        private void OnBannerProviderMaxRetryReached()
        {
            // Add the current provider to the list of tried providers
            triedBannerProviders.Add(currentBannerProvider);

            // Unsubscribe from the current provider's events
            UnsubscribeFromBannerProviderEvents(currentBannerProvider);

            // Try the next provider
            SelectNextBannerProvider();
        }

        #region Banner Ad Event Handlers

        /// <summary>
        /// Called when a banner ad is loaded.
        /// </summary>
        private void HandleBannerAdLoaded()
        {
            // Forward the event to subscribers
            onBannerAdLoaded.Invoke();
        }

        /// <summary>
        /// Called when a banner ad impression is recorded.
        /// </summary>
        private void HandleBannerAdImpression()
        {
            // Forward the event to subscribers
            onBannerAdImpression.Invoke();

            // Ad was successfully shown, no need to try other providers
            // For banner ads, we completely unsubscribe from the current provider's events
            // This ensures we don't have lingering event subscriptions
            // When we call ShowBannerAd() again, we'll resubscribe as needed
            UnsubscribeFromBannerProviderEvents(currentBannerProvider);
        }

        /// <summary>
        /// Called when a banner ad is clicked.
        /// </summary>
        private void HandleBannerAdClick()
        {
            // Forward the event to subscribers
            onBannerAdClick.Invoke();
        }

        /// <summary>
        /// Called when a banner ad error occurs.
        /// </summary>
        /// <param name="errorMessage">The error message.</param>
        private void HandleBannerAdError(string errorMessage)
        {
            // Forward the event to subscribers
            onBannerAdError.Invoke(errorMessage);
        }

        #endregion

        #endregion

        #region Interstitial Ad Methods

        /// <summary>
        /// Selects the next interstitial ad provider based on the configured selection strategy.
        /// </summary>
        private void SelectNextInterstitialProvider()
        {
            // Unsubscribe from previous provider events
            UnsubscribeFromInterstitialProviderEvents(currentInterstitialProvider);

            // Get the next provider
            currentInterstitialProvider = GetNextProvider(interstitialProviders, triedInterstitialProviders);

            // If no provider is available, log a warning and return
            if (currentInterstitialProvider == null)
            {
                Debug.LogWarning("No available interstitial ad providers.");
                return;
            }

            // Subscribe to the provider's events
            SubscribeToInterstitialProviderEvents(currentInterstitialProvider);

            // Show the ad - the provider will handle CanShowAd checks internally
            currentInterstitialProvider.ShowAd();
        }

        /// <summary>
        /// Subscribes to the events of an interstitial ad provider.
        /// </summary>
        private void SubscribeToInterstitialProviderEvents(BaseInterstitialAd provider)
        {
            provider.OnMaxRetryAttemptsReached.AddListener(OnInterstitialProviderMaxRetryReached);
            provider.OnAdImpression.AddListener(HandleInterstitialAdImpression);
            provider.OnAdClick.AddListener(HandleInterstitialAdClick);
            provider.OnAdLoad.AddListener(HandleInterstitialAdLoaded);
            provider.OnAdError.AddListener(HandleInterstitialAdError);
        }

        /// <summary>
        /// Unsubscribes from the events of an interstitial ad provider.
        /// </summary>
        private void UnsubscribeFromInterstitialProviderEvents(BaseInterstitialAd provider)
        {
            if (provider == null)
                return;

            provider.OnMaxRetryAttemptsReached.RemoveListener(OnInterstitialProviderMaxRetryReached);
            provider.OnAdImpression.RemoveListener(HandleInterstitialAdImpression);
            provider.OnAdClick.RemoveListener(HandleInterstitialAdClick);
            provider.OnAdLoad.RemoveListener(HandleInterstitialAdLoaded);
            provider.OnAdError.RemoveListener(HandleInterstitialAdError);
        }

        /// <summary>
        /// Called when an interstitial ad provider reaches its maximum retry attempts.
        /// </summary>
        private void OnInterstitialProviderMaxRetryReached()
        {
            // Add the current provider to the list of tried providers
            triedInterstitialProviders.Add(currentInterstitialProvider);

            // Unsubscribe from the current provider's events
            UnsubscribeFromInterstitialProviderEvents(currentInterstitialProvider);

            // Try the next provider
            SelectNextInterstitialProvider();
        }

        #region Interstitial Ad Event Handlers

        /// <summary>
        /// Called when an interstitial ad is loaded.
        /// </summary>
        private void HandleInterstitialAdLoaded()
        {
            // Forward the event to subscribers
            onInterstitialAdLoaded.Invoke();
        }

        /// <summary>
        /// Called when an interstitial ad impression is recorded.
        /// </summary>
        private void HandleInterstitialAdImpression()
        {
            // Forward the event to subscribers
            onInterstitialAdImpression.Invoke();

            // Ad was successfully shown, no need to try other providers
            // Unsubscribe from all events since interstitial ads are one-time
            UnsubscribeFromInterstitialProviderEvents(currentInterstitialProvider);
            currentInterstitialProvider = null;
        }

        /// <summary>
        /// Called when an interstitial ad is clicked.
        /// </summary>
        private void HandleInterstitialAdClick()
        {
            // Forward the event to subscribers
            onInterstitialAdClick.Invoke();
        }

        /// <summary>
        /// Called when an interstitial ad error occurs.
        /// </summary>
        /// <param name="errorMessage">The error message.</param>
        private void HandleInterstitialAdError(string errorMessage)
        {
            // Forward the event to subscribers
            onInterstitialAdError.Invoke(errorMessage);
        }

        #endregion

        #endregion

        #region Rewarded Ad Methods

        /// <summary>
        /// Selects the next rewarded ad provider based on the configured selection strategy.
        /// </summary>
        private void SelectNextRewardedProvider()
        {
            // Unsubscribe from previous provider events
            UnsubscribeFromRewardedProviderEvents(currentRewardedProvider);

            // Get the next provider
            currentRewardedProvider = GetNextProvider(rewardedProviders, triedRewardedProviders);

            // If no provider is available, log a warning and return
            if (currentRewardedProvider == null)
            {
                Debug.LogWarning("No available rewarded ad providers.");
                return;
            }

            // Subscribe to the provider's events
            SubscribeToRewardedProviderEvents(currentRewardedProvider);

            // Show the ad - the provider will handle CanShowAd checks internally
            currentRewardedProvider.ShowAd();
        }

        /// <summary>
        /// Subscribes to the events of a rewarded ad provider.
        /// </summary>
        private void SubscribeToRewardedProviderEvents(BaseRewardedAd provider)
        {
            provider.OnMaxRetryAttemptsReached.AddListener(OnRewardedProviderMaxRetryReached);
            provider.OnAdImpression.AddListener(HandleRewardedAdImpression);
            provider.OnAdClick.AddListener(HandleRewardedAdClick);
            provider.OnAdLoad.AddListener(HandleRewardedAdLoaded);
            provider.OnAdError.AddListener(HandleRewardedAdError);
            provider.OnAdReward.AddListener(HandleRewardedAdReward);
        }

        /// <summary>
        /// Unsubscribes from the events of a rewarded ad provider.
        /// </summary>
        private void UnsubscribeFromRewardedProviderEvents(BaseRewardedAd provider)
        {
            if (provider == null)
                return;

            provider.OnMaxRetryAttemptsReached.RemoveListener(OnRewardedProviderMaxRetryReached);
            provider.OnAdImpression.RemoveListener(HandleRewardedAdImpression);
            provider.OnAdClick.RemoveListener(HandleRewardedAdClick);
            provider.OnAdLoad.RemoveListener(HandleRewardedAdLoaded);
            provider.OnAdError.RemoveListener(HandleRewardedAdError);
            provider.OnAdReward.RemoveListener(HandleRewardedAdReward);
        }

        /// <summary>
        /// Called when a rewarded ad provider reaches its maximum retry attempts.
        /// </summary>
        private void OnRewardedProviderMaxRetryReached()
        {
            // Add the current provider to the list of tried providers
            triedRewardedProviders.Add(currentRewardedProvider);

            // Unsubscribe from the current provider's events
            UnsubscribeFromRewardedProviderEvents(currentRewardedProvider);

            // Try the next provider
            SelectNextRewardedProvider();
        }

        #region Rewarded Ad Event Handlers

        /// <summary>
        /// Called when a rewarded ad is loaded.
        /// </summary>
        private void HandleRewardedAdLoaded()
        {
            // Forward the event to subscribers
            onRewardedAdLoaded.Invoke();
        }

        /// <summary>
        /// Called when a rewarded ad impression is recorded.
        /// </summary>
        private void HandleRewardedAdImpression()
        {
            // Forward the event to subscribers
            onRewardedAdImpression.Invoke();

            // Ad was successfully shown, but we need to keep the subscription for the reward event
            if (currentRewardedProvider != null)
            {
                // For rewarded ads, we only remove the MaxRetryAttemptsReached listener
                // We need to keep the OnAdReward listener active to receive the reward
                currentRewardedProvider.OnMaxRetryAttemptsReached.RemoveListener(OnRewardedProviderMaxRetryReached);

                // Note: We intentionally keep the OnAdReward listener active
                // It will be removed in HandleRewardedAdReward() when the reward is granted
            }
        }

        /// <summary>
        /// Called when a rewarded ad is clicked.
        /// </summary>
        private void HandleRewardedAdClick()
        {
            // Forward the event to subscribers
            onRewardedAdClick.Invoke();
        }

        /// <summary>
        /// Called when a rewarded ad error occurs.
        /// </summary>
        /// <param name="errorMessage">The error message.</param>
        private void HandleRewardedAdError(string errorMessage)
        {
            // Forward the event to subscribers
            onRewardedAdError.Invoke(errorMessage);
        }

        /// <summary>
        /// Called when a user earns a reward from watching a rewarded ad.
        /// </summary>
        private void HandleRewardedAdReward()
        {
            // Forward the reward event
            onUserEarnedRewardedAd.Invoke();

            // Unsubscribe from all events since rewarded ads are one-time
            UnsubscribeFromRewardedProviderEvents(currentRewardedProvider);
            currentRewardedProvider = null;
        }

        #endregion

        #endregion

        #region Initialization and Loading

        /// <summary>
        /// Initializes all ad providers.
        /// </summary>
        private void InitializeAllProviders()
        {
            // Initialize all types of ad providers
            InitializeProviders(bannerProviders);
            InitializeProviders(interstitialProviders);
            InitializeProviders(rewardedProviders);

            Debug.Log("AdManager: All ad providers initialized.");
        }

        /// <summary>
        /// Initializes a list of ad providers.
        /// </summary>
        /// <typeparam name="T">The type of ad provider.</typeparam>
        /// <param name="providers">The list of providers to initialize.</param>
        private void InitializeProviders<T>(List<T> providers) where T : BaseAd
        {
            if (providers == null || providers.Count == 0)
            {
                return;
            }

            foreach (var provider in providers)
            {
                if (provider != null && !provider.AdInitialized)
                {
                    provider.Initialize();
                }
            }
        }

        /// <summary>
        /// Loads all ad providers.
        /// </summary>
        private void LoadAllProviders()
        {
            // Load all types of ad providers
            LoadProviders(bannerProviders);
            LoadProviders(interstitialProviders);
            LoadProviders(rewardedProviders);

            Debug.Log("AdManager: All ad providers loaded.");
        }

        /// <summary>
        /// Loads a list of ad providers.
        /// </summary>
        /// <typeparam name="T">The type of ad provider.</typeparam>
        /// <param name="providers">The list of providers to load.</param>
        private void LoadProviders<T>(List<T> providers) where T : BaseAd
        {
            if (providers == null || providers.Count == 0)
            {
                return;
            }

            foreach (var provider in providers)
            {
                if (provider != null && provider.AdInitialized && !provider.AdLoaded)
                {
                    provider.LoadAd();
                }
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets the next provider from the list based on the configured selection strategy.
        /// </summary>
        /// <typeparam name="T">The type of ad provider.</typeparam>
        /// <param name="allProviders">The list of all available providers.</param>
        /// <param name="triedProviders">The list of providers that have already been tried.</param>
        /// <returns>The next provider to try, or null if no providers are available.</returns>
        private T GetNextProvider<T>(List<T> allProviders, List<T> triedProviders) where T : BaseAd
        {
            // Create a list of available providers (those that haven't been tried yet)
            List<T> availableProviders = new();

            foreach (T provider in allProviders)
            {
                if (provider != null && !triedProviders.Contains(provider))
                {
                    availableProviders.Add(provider);
                }
            }

            // If no providers are available, return null
            if (availableProviders.Count == 0)
            {
                return null;
            }

            // Select a provider based on the selection strategy
            if (useRandomProviderSelection)
            {
                // Random selection strategy
                int randomIndex = Random.Range(0, availableProviders.Count);
                return availableProviders[randomIndex];
            }
            else
            {
                // Sequential selection strategy - return the first available provider
                return availableProviders[0];
            }
        }

        #endregion
    }
}