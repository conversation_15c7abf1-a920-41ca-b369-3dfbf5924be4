using System;
using System.Collections.Generic;
using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// A thread-safe service locator for dependency management.
    ///
    /// <para>
    /// The ServiceLocator provides a centralized registry for services in your application.
    /// It allows components to access shared services without direct dependencies.
    /// </para>
    /// </summary>
    public static class ServiceLocator
    {
        private static readonly Dictionary<Type, object> services = new();
        private static readonly object lockObject = new();

        /// <summary>
        /// Checks if a service of the specified type is registered.
        /// </summary>
        /// <typeparam name="T">The type of service to check for.</typeparam>
        /// <returns>True if the service is registered, false otherwise.</returns>
        public static bool IsRegistered<T>() where T : class
        {
            Type type = typeof(T);
            lock (lockObject)
            {
                return services.ContainsKey(type);
            }
        }

        /// <summary>
        /// Registers a service instance for the specified type.
        /// </summary>
        /// <typeparam name="T">The type to register the service as.</typeparam>
        /// <param name="service">The service instance to register.</param>
        public static void Register<T>(T service) where T : class
        {
            if (service == null)
            {
                Debug.LogError("Service cannot be null");
                return;
            }

            Type type = typeof(T);
            lock (lockObject)
            {
                if (services.ContainsKey(type))
                {
                    Debug.LogWarning($"Service of type {type.Name} is being overwritten.");
                }
                services[type] = service;
            }
        }

        /// <summary>
        /// Registers a service instance only if it doesn't already exist.
        /// </summary>
        /// <typeparam name="T">The type to register the service as.</typeparam>
        /// <param name="service">The service instance to register.</param>
        /// <returns>True if the service was registered, false if it already existed or service was null.</returns>
        public static bool RegisterIfAbsent<T>(T service) where T : class
        {
            if (service == null)
            {
                Debug.LogError("Service cannot be null");
                return false;
            }

            Type type = typeof(T);
            lock (lockObject)
            {
                if (!services.ContainsKey(type))
                {
                    services[type] = service;
                    return true;
                }
                return false;
            }
        }

        /// <summary>
        /// Removes a service of the specified type from the registry.
        /// </summary>
        /// <typeparam name="T">The type of service to unregister.</typeparam>
        /// <returns>True if the service was removed, false if it wasn't registered.</returns>
        public static bool Unregister<T>() where T : class
        {
            Type type = typeof(T);
            lock (lockObject)
            {
                return services.Remove(type);
            }
        }

        /// <summary>
        /// Gets a service of the specified type.
        /// </summary>
        /// <typeparam name="T">The type of service to retrieve.</typeparam>
        /// <returns>The registered service instance, or null if not found.</returns>
        public static T Get<T>() where T : class
        {
            Type type = typeof(T);
            lock (lockObject)
            {
                if (services.TryGetValue(type, out object service))
                {
                    return (T)service;
                }

                Debug.LogWarning($"Service of type {type.Name} is not registered.");
                return null;
            }
        }

        /// <summary>
        /// Tries to get a service of the specified type.
        /// </summary>
        /// <typeparam name="T">The type of service to retrieve.</typeparam>
        /// <param name="service">When this method returns, contains the service instance if found; otherwise, null.</param>
        /// <returns>True if the service was found, false otherwise.</returns>
        public static bool TryGet<T>(out T service) where T : class
        {
            Type type = typeof(T);
            lock (lockObject)
            {
                if (services.TryGetValue(type, out object serviceObj))
                {
                    service = (T)serviceObj;
                    return true;
                }
            }
            service = null;
            return false;
        }

        /// <summary>
        /// Clears all registered services.
        /// </summary>
        public static void Clear()
        {
            lock (lockObject)
            {
                services.Clear();
            }
        }
    }
}